const nodemailer = require('nodemailer');

// Array de configurações SMTP para teste
const smtpConfigs = [
  {
    name: 'Gmail',
    config: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // true para 465, false para outras portas
      auth: {
        user: '<EMAIL>',
        pass: 'sua-senha-de-app' // Use App Password para Gmail
      }
    }
  },
  {
    name: 'Outlook/Hotmail',
    config: {
      host: 'smtp-mail.outlook.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'sua-senha'
      }
    }
  },
  {
    name: 'Yahoo',
    config: {
      host: 'smtp.mail.yahoo.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'sua-senha-de-app' // Use App Password para Yahoo
      }
    }
  },
  {
    name: 'SMTP Personalizado',
    config: {
      host: 'smtp.seudominio.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'sua-senha'
      }
    }
  }
];

// Configurações do e-mail de teste
const mailOptions = {
  from: '"Teste SMTP" <<EMAIL>>',
  to: '<EMAIL>',
  subject: 'Teste de Envio SMTP - ' + new Date().toLocaleString(),
  text: 'Este é um e-mail de teste enviado via SMTP.',
  html: `
    <h2>Teste de Envio SMTP</h2>
    <p>Este é um e-mail de teste enviado via SMTP.</p>
    <p><strong>Data/Hora:</strong> ${new Date().toLocaleString()}</p>
    <p><strong>Servidor:</strong> {{serverName}}</p>
  `
};

// Função para testar uma configuração SMTP
async function testSmtpConfig(smtpConfig) {
  console.log(`\n🔄 Testando configuração: ${smtpConfig.name}`);
  console.log(`   Host: ${smtpConfig.config.host}:${smtpConfig.config.port}`);
  
  try {
    // Criar transporter
    const transporter = nodemailer.createTransporter(smtpConfig.config);
    
    // Verificar conexão
    console.log('   ⏳ Verificando conexão...');
    await transporter.verify();
    console.log('   ✅ Conexão estabelecida com sucesso!');
    
    // Preparar e-mail personalizado
    const customMailOptions = {
      ...mailOptions,
      html: mailOptions.html.replace('{{serverName}}', smtpConfig.name),
      from: smtpConfig.config.auth.user, // Usar o e-mail do remetente configurado
    };
    
    // Enviar e-mail de teste
    console.log('   ⏳ Enviando e-mail de teste...');
    const info = await transporter.sendMail(customMailOptions);
    
    console.log('   ✅ E-mail enviado com sucesso!');
    console.log(`   📧 Message ID: ${info.messageId}`);
    if (info.response) {
      console.log(`   📝 Resposta do servidor: ${info.response}`);
    }
    
    return { success: true, config: smtpConfig.name, messageId: info.messageId };
    
  } catch (error) {
    console.log('   ❌ Erro ao testar configuração:');
    console.log(`   🔍 Detalhes: ${error.message}`);
    
    // Sugestões baseadas no tipo de erro
    if (error.code === 'EAUTH') {
      console.log('   💡 Sugestão: Verifique as credenciais de autenticação');
    } else if (error.code === 'ECONNECTION') {
      console.log('   💡 Sugestão: Verifique o host e porta do servidor SMTP');
    } else if (error.code === 'ESOCKET') {
      console.log('   💡 Sugestão: Problema de conectividade de rede');
    }
    
    return { success: false, config: smtpConfig.name, error: error.message };
  }
}

// Função principal
async function runTests() {
  console.log('🚀 Iniciando testes de configurações SMTP...');
  console.log('=' .repeat(50));
  
  const results = [];
  
  // Testar cada configuração
  for (const smtpConfig of smtpConfigs) {
    const result = await testSmtpConfig(smtpConfig);
    results.push(result);
    
    // Pausa entre testes para evitar rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Resumo dos resultados
  console.log('\n' + '=' .repeat(50));
  console.log('📊 RESUMO DOS TESTES');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Configurações bem-sucedidas: ${successful.length}`);
  successful.forEach(r => {
    console.log(`   - ${r.config} (ID: ${r.messageId})`);
  });
  
  console.log(`❌ Configurações com falha: ${failed.length}`);
  failed.forEach(r => {
    console.log(`   - ${r.config}: ${r.error}`);
  });
  
  console.log('\n💡 DICAS IMPORTANTES:');
  console.log('- Para Gmail/Yahoo: Use senhas de aplicativo, não a senha normal');
  console.log('- Verifique se a autenticação de 2 fatores está configurada');
  console.log('- Alguns provedores podem bloquear aplicações "menos seguras"');
  console.log('- Atualize as credenciais no array smtpConfigs antes de executar');
}

// Executar testes se o script for chamado diretamente
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testSmtpConfig, smtpConfigs, mailOptions };
