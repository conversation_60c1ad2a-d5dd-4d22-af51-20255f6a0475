// Exemplo de como personalizar as configurações SMTP
// Copie este arquivo para config.js e ajuste suas credenciais

const customSmtpConfigs = [
  {
    name: 'Gmail - Produção',
    config: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'sua-senha-de-app-aqui' // Gere em: https://myaccount.google.com/apppasswords
      }
    }
  },
  {
    name: 'Servidor SMTP Corporativo',
    config: {
      host: 'mail.suaempresa.com',
      port: 587,
      secure: false, // ou true para porta 465
      auth: {
        user: '<EMAIL>',
        pass: 'senha-corporativa'
      },
      // Opções adicionais se necessário
      tls: {
        rejectUnauthorized: false // apenas para servidores com certificados auto-assinados
      }
    }
  },
  {
    name: 'SendG<PERSON>',
    config: {
      host: 'smtp.sendgrid.net',
      port: 587,
      secure: false,
      auth: {
        user: 'apikey',
        pass: 'SG.sua-api-key-aqui'
      }
    }
  },
  {
    name: 'Mailgun',
    config: {
      host: 'smtp.mailgun.org',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'sua-senha-mailgun'
      }
    }
  }
];

// Configurações personalizadas do e-mail
const customMailOptions = {
  from: '"Seu Nome" <<EMAIL>>',
  to: '<EMAIL>', // Altere para um e-mail válido para teste
  subject: 'Teste SMTP - ' + new Date().toLocaleString(),
  text: 'E-mail de teste enviado via script Node.js',
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">🧪 Teste de Configuração SMTP</h2>
      <p>Este e-mail foi enviado com sucesso através do servidor: <strong>{{serverName}}</strong></p>
      <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3>Detalhes do Teste:</h3>
        <ul>
          <li><strong>Data/Hora:</strong> ${new Date().toLocaleString()}</li>
          <li><strong>Servidor:</strong> {{serverName}}</li>
          <li><strong>Script:</strong> Node.js + Nodemailer</li>
        </ul>
      </div>
      <p style="color: #666; font-size: 12px;">
        Este é um e-mail automático de teste. Não é necessário responder.
      </p>
    </div>
  `
};

module.exports = {
  smtpConfigs: customSmtpConfigs,
  mailOptions: customMailOptions
};
