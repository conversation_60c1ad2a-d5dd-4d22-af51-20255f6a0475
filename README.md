# 📧 Mail Test - Script de Teste SMTP

Script em Node.js para testar configurações de envio de e-mail através de diferentes servidores SMTP.

## 🚀 Instalação

1. Instale as dependências:
```bash
npm install
```

## ⚙️ Configuração

### Opção 1: Editar diretamente o arquivo principal
Edite o array `smtpConfigs` no arquivo `mail-test.js` com suas credenciais.

### Opção 2: Usar arquivo de configuração personalizado
1. Copie o arquivo de exemplo:
```bash
cp config-example.js config.js
```

2. Edite o arquivo `config.js` com suas credenciais reais.

3. Modifique o `mail-test.js` para importar suas configurações:
```javascript
// No início do arquivo mail-test.js, substitua:
const { smtpConfigs, mailOptions } = require('./config.js');
```

## 🔧 Configurações Suportadas

O script inclui exemplos para:
- **Gmail** (requer senha de aplicativo)
- **Outlook/Hotmail**
- **Yahoo** (requer senha de aplicativo)
- **Servidores SMTP personalizados**
- **SendGrid**
- **Mailgun**

## 🏃‍♂️ Execução

Execute o teste:
```bash
npm test
# ou
npm start
# ou
node mail-test.js
```

## 📋 Exemplo de Saída

```
🚀 Iniciando testes de configurações SMTP...
==================================================

🔄 Testando configuração: Gmail
   Host: smtp.gmail.com:587
   ⏳ Verificando conexão...
   ✅ Conexão estabelecida com sucesso!
   ⏳ Enviando e-mail de teste...
   ✅ E-mail enviado com sucesso!
   📧 Message ID: <<EMAIL>>

==================================================
📊 RESUMO DOS TESTES
==================================================
✅ Configurações bem-sucedidas: 1
   - Gmail (ID: <<EMAIL>>)
❌ Configurações com falha: 0

💡 DICAS IMPORTANTES:
- Para Gmail/Yahoo: Use senhas de aplicativo, não a senha normal
- Verifique se a autenticação de 2 fatores está configurada
- Alguns provedores podem bloquear aplicações "menos seguras"
- Atualize as credenciais no array smtpConfigs antes de executar
```

## 🔐 Configuração de Senhas de Aplicativo

### Gmail
1. Ative a verificação em 2 etapas
2. Acesse: https://myaccount.google.com/apppasswords
3. Gere uma senha de aplicativo
4. Use essa senha no lugar da sua senha normal

### Yahoo
1. Ative a verificação em 2 etapas
2. Acesse: https://login.yahoo.com/account/security
3. Gere uma senha de aplicativo
4. Use essa senha no lugar da sua senha normal

## 🛠️ Personalização

### Modificar o E-mail de Teste
Edite o objeto `mailOptions` para personalizar:
- Remetente (`from`)
- Destinatário (`to`)
- Assunto (`subject`)
- Conteúdo texto (`text`)
- Conteúdo HTML (`html`)

### Adicionar Novas Configurações SMTP
Adicione novos objetos ao array `smtpConfigs`:
```javascript
{
  name: 'Meu Servidor',
  config: {
    host: 'smtp.meuservidor.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'minha-senha'
    }
  }
}
```

## 🐛 Solução de Problemas

### Erro de Autenticação (EAUTH)
- Verifique usuário e senha
- Para Gmail/Yahoo, use senhas de aplicativo
- Verifique se a autenticação em 2 fatores está ativa

### Erro de Conexão (ECONNECTION)
- Verifique host e porta
- Teste conectividade de rede
- Verifique firewall/proxy

### Erro de Socket (ESOCKET)
- Problema de conectividade
- Tente alterar a porta (587, 465, 25)
- Verifique configuração `secure`

## 📝 Licença

MIT
